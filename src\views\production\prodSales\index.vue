<template>
  <div class="production-sales-model">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <h1>产销配比模型</h1>
        <div style="float: right; margin-top: -30px">
          选择模型：
          <el-select v-model="modelType" placeholder="请选择">
            <el-option label="最大产量模型" value="1"></el-option>
            <el-option label="最大销售额模型" value="2"></el-option>
          </el-select>
        </div>
      </div>

      <!-- 基础参数设定 -->
      <el-card shadow="hover" class="section-card">
        <div slot="header">
          <h2>基础参数设定</h2>
        </div>
        <el-form :model="form" :inline="true" label-position="left">
          <el-form-item label="产量单位：">
            <el-select v-model="form.productionUnit" placeholder="请选择">
              <el-option label="亿方" value="亿方"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="计划产量目标：">
            <el-input
              v-model="form.productionTarget"
              placeholder="请输入"
              type="number"
            ></el-input>
          </el-form-item>
          <el-form-item label="利润目标：" v-if="modelType === '1'">
            <el-input
              v-model="form.profitTarget"
              placeholder="请输入"
              type="number"
            ></el-input>
          </el-form-item>
        </el-form>
      </el-card>

      <div class="section-box">
        <!-- 终端销售渠道及价格 -->
        <el-card shadow="hover" class="section-card card-left">
          <div slot="header">
            <h2>终端销售渠道及价格</h2>
          </div>
          <el-table :data="salesChannels" border style="width: 100%">
            <el-table-column
              type="index"
              label="序号"
              width="100"
            ></el-table-column>
            <el-table-column
              prop="channel"
              label="渠道"
              width="180"
            ></el-table-column>
            <el-table-column label="价格">
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.price"
                  placeholder="请输入"
                  type="number"
                ></el-input>
              </template>
            </el-table-column>
            <el-table-column label="膨胀系数">
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.expansionCoefficient"
                  placeholder="请输入"
                  type="number"
                ></el-input>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 渠道配置 -->
        <el-card shadow="hover" class="section-card card-right">
          <div slot="header">
            <h2>渠道配置</h2>
          </div>
          <ConstraintConfig ref="constraintConfig" />
        </el-card>
      </div>

      <!-- 约束条件配置 -->
      <el-card shadow="hover" class="section-card">
        <div slot="header">
          <h2>约束条件配置</h2>
        </div>

        <div class="section-box">
          <div class="card-left">
            <h3>渠道供应策略</h3>
            <el-table :data="supplyStrategy" border style="width: 100%">
              <el-table-column
                type="index"
                label="序号"
                width="100"
              ></el-table-column>
              <el-table-column
                prop="channel"
                label="渠道"
                width="180"
              ></el-table-column>
              <el-table-column label="最小供应量">
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.minSupply"
                    placeholder="请输入"
                    type="number"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="最大接收量">
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.maxReceive"
                    placeholder="请输入"
                    type="number"
                  ></el-input>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <div class="card-right">
            <h3>气田产量约束</h3>
            <el-table :data="fieldConstraints" border style="width: 100%">
              <el-table-column
                type="index"
                label="序号"
                width="100"
              ></el-table-column>
              <el-table-column
                prop="field"
                label="气田"
                width="180"
              ></el-table-column>
              <el-table-column label="气田最低产量">
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.minProduction"
                    placeholder="请输入"
                    type="number"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="气田最高产量">
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.maxProduction"
                    placeholder="请输入"
                    type="number"
                  ></el-input>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-card>

      <div class="action-buttons" v-if="!isCalculating">
        <el-button
          type="primary"
          size="large"
          @click="startCalculation"
          :loading="isLoading"
          :disabled="isLoading || !modelType"
        >
          {{ getButtonText() }}
        </el-button>
      </div>

      <el-card shadow="hover" class="section-card" v-else>
        <div slot="header" class="card-header">
          <h2>测算结果</h2>
          <el-button type="primary" @click="resetCalculation">重置</el-button>
        </div>
        <div class="section-box">
          <div class="card-left">
            <el-table
              :data="processedTableData"
              border
              style="width: 100%"
              show-summary
              :summary-method="getSummaries"
            >
              <el-table-column
                prop="rowHeader"
                label=""
                width="120"
                fixed="left"
              />
              <el-table-column
                v-for="column in dynamicColumns"
                :key="column.prop"
                :prop="column.prop"
                :label="column.label"
                align="center"
              />
              <el-table-column
                prop="rowTotal"
                label="总计"
                width="100"
                align="center"
                fixed="right"
              />
            </el-table>
          </div>

          <div class="card-right">
            <div class="result-title">
              <div class="result-title-text">
                最大{{ modelType === "1" ? "产量" : "销售额" }}
              </div>
              <div class="result-title-formula">
                <!-- Z=P1X31 +P2(X32+X42)+P3(X13+X23+X33) -->
                {{ apiResponseData.data.maxQ || apiResponseData.data.maxValue }}
              </div>
            </div>
            <div class="chart-box">
              <SalesVolume :chartData="chartData"></SalesVolume>
            </div>
          </div>
        </div>
      </el-card>
    </el-card>
  </div>
</template>

<script>
import SalesVolume from "./salesVolume.vue";
import ConstraintConfig from "./ConstraintConfig.vue";
import { maxOutput, maxSales } from "@/api/production/prodSales";
import { getChineseFieldName, getOrganizationProp } from "./fieldMapping.js";

export default {
  name: "prodSales",
  components: {
    SalesVolume,
    ConstraintConfig,
  },
  data() {
    return {
      modelType: "",
      isCalculating: false,
      isLoading: false,
      form: {
        productionUnit: "",
        productionTarget: "29.79",
        profitTarget: "",
      },
      salesChannels: [
        {
          id: 1,
          channel: "香港中电",
          price: "2.93",
          expansionCoefficient: "1",
        },
        {
          id: 2,
          channel: "气电南山",
          price: "1.52",
          expansionCoefficient: "1.1",
        },
        {
          id: 3,
          channel: "气电广东",
          price: "2.04",
          expansionCoefficient: "1",
        },
      ],
      channels: ["崖城13-1", "崖城13-10", "陵水17-2", "陵水25-1"],
      selectedChannels: [],
      demandCenters: ["气电南山", "气电广东"],
      selectedDemandCenters: [],
      supplyStrategy: [
        { id: 1, channel: "香港中电", minSupply: "14.29", maxReceive: "15.25" },
        { id: 2, channel: "气电南山", minSupply: "3.01", maxReceive: "3.82" },
        { id: 3, channel: "气电广东", minSupply: "14.78", maxReceive: "16.27" },
      ],
      fieldConstraints: [
        {
          id: 1,
          field: "崖城13-1",
          minProduction: "2.00",
          maxProduction: "2.55",
        },
        { id: 2, field: "崖城13-10", minProduction: "0", maxProduction: "0" },
        {
          id: 3,
          field: "陵水17-2",
          minProduction: "30.01",
          maxProduction: "32.0",
        },
        {
          id: 4,
          field: "陵水25-1",
          minProduction: "0.42",
          maxProduction: "0.55",
        },
      ],
      // 原始API响应数据
      apiResponseData: null,
    };
  },
  computed: {
    /**
     * 获取所有唯一的列键（除了name字段）
     */
    allKeys() {
      if (!this.apiResponseData?.data?.result) {
        return [];
      }

      const data = this.apiResponseData.data.result;
      const keys = new Set();

      data.forEach((item) => {
        Object.keys(item).forEach((key) => {
          if (key !== "name") {
            keys.add(key);
          }
        });
      });

      return Array.from(keys).sort();
    },

    /**
     * 动态生成表格列配置（转置后：气田作为列）
     */
    dynamicColumns() {
      return this.allKeys.map((key) => ({
        prop: this.getFieldColumnProp(key),
        label: getChineseFieldName(key), // 使用中文气田名称作为列标题
      }));
    },

    /**
     * 处理后的表格数据（行列转置）
     * 机构名称作为行，气田名称作为列
     * 应用字段映射将英文字段名转换为中文显示名称
     */
    processedTableData() {
      if (!this.apiResponseData?.data?.result) {
        return [];
      }

      const data = this.apiResponseData.data.result;
      const tableData = [];

      // 为每个机构创建一行
      data.forEach((item) => {
        const row = {
          rowHeader: item.name, // 机构名称作为行标题
          rowTotal: 0,
        };

        // 为每个气田字段创建对应的列值
        this.allKeys.forEach((key) => {
          const columnProp = this.getFieldColumnProp(key); // 使用气田字段作为列属性
          const value = item[key] || 0;
          row[columnProp] = this.formatNumber(value);
          row.rowTotal += Number(value);
        });

        // 格式化行总计
        row.rowTotal = this.formatNumber(row.rowTotal);
        tableData.push(row);
      });

      return tableData;
    },

    /**
     * 为图表组件准备的数据（转置后：展示各气田的总产量）
     */
    chartData() {
      if (!this.apiResponseData?.data?.result) {
        return [];
      }

      const data = this.apiResponseData.data.result;

      // 计算每个气田的总产量
      return this.allKeys.map((key) => {
        const total = data.reduce((sum, item) => {
          return sum + Number(item[key] || 0);
        }, 0);

        return {
          name: getChineseFieldName(key), // 使用中文气田名称
          value: Number(total.toFixed(2)),
        };
      });
    },
  },
  methods: {
    /**
     * 获取按钮文本
     */
    getButtonText() {
      if (this.isLoading) {
        return "测算中...";
      }
      if (!this.modelType) {
        return "请选择模型";
      }
      return this.modelType === "1"
        ? "开始测算（最大产量）"
        : "开始测算（最大销售额）";
    },

    /**
     * 格式化数值，保留两位小数
     */
    formatNumber(value) {
      if (value === null || value === undefined || value === "") {
        return "0.00";
      }
      const num = Number(value);
      if (isNaN(num)) {
        return "0.00";
      }
      return num.toFixed(2);
    },

    /**
     * 清理属性名，使其适合作为Vue属性
     */
    sanitizeProp(name) {
      // 使用字典映射来处理机构名称
      return getOrganizationProp(name);
    },

    /**
     * 获取气田字段的列属性名
     */
    getFieldColumnProp(fieldKey) {
      // 将气田字段转换为适合作为Vue属性的格式
      return fieldKey.replace(/[^a-zA-Z0-9]/g, "_").toLowerCase();
    },

    /**
     * 计算总计行（转置后的结构）
     */
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];

      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "总计";
          return;
        }

        if (column.property === "rowTotal") {
          // 计算所有机构的总计（右侧总计列）
          const total = data.reduce(
            (sum, row) => sum + Number(row.rowTotal || 0),
            0
          );
          sums[index] = this.formatNumber(total);
          return;
        }

        // 计算每个气田的总计（底部总计行）
        const values = data.map((item) => Number(item[column.property] || 0));
        const total = values.reduce((sum, value) => sum + value, 0);
        sums[index] = this.formatNumber(total);
      });

      return sums;
    },
    /**
     * 收集并格式化表单数据为API所需格式
     */
    collectFormData() {
      // 检查是否选择了模型
      if (!this.modelType) {
        this.$message.error("请先选择模型类型");
        return null;
      }

      // 使用新的验证方法
      const errors = this.validateForm();
      if (errors.length > 0) {
        this.$message.error(`请填写以下必需字段: ${errors.join(", ")}`);
        return null;
      }

      // 格式化数据
      const apiData = {
        // 膨胀系数参数
        f1: Number(this.salesChannels[0].expansionCoefficient), // 香港中电
        f2: Number(this.salesChannels[1].expansionCoefficient), // 气电南山
        f3: Number(this.salesChannels[2].expansionCoefficient), // 气电广东

        // 价格参数
        p1: Number(this.salesChannels[0].price), // 香港中电
        p2: Number(this.salesChannels[1].price), // 气电南山
        p3: Number(this.salesChannels[2].price), // 气电广东

        // 接收量/供应量参数
        m1Max: Number(this.supplyStrategy[0].maxReceive), // 香港中电最大接收量
        m1Min: Number(this.supplyStrategy[0].minSupply), // 香港中电最小供应量
        m2Max: Number(this.supplyStrategy[1].maxReceive), // 气电南山最大接收量
        m2Min: Number(this.supplyStrategy[1].minSupply), // 气电南山最小供应量
        m3Max: Number(this.supplyStrategy[2].maxReceive), // 气电广东最大接收量
        m3Min: Number(this.supplyStrategy[2].minSupply), // 气电广东最小供应量

        // 气田产量参数
        maMax: Number(this.fieldConstraints[0].maxProduction), // 崖城13-1最高产量
        maMin: Number(this.fieldConstraints[0].minProduction), // 崖城13-1最低产量
        mbMax: Number(this.fieldConstraints[1].maxProduction), // 崖城13-10最高产量
        mbMin: Number(this.fieldConstraints[1].minProduction), // 崖城13-10最低产量
        mcMax: Number(this.fieldConstraints[2].maxProduction), // 陵水17-2最高产量
        mcMin: Number(this.fieldConstraints[2].minProduction), // 陵水17-2最低产量
        mdMax: Number(this.fieldConstraints[3].maxProduction), // 陵水25-1最高产量
        mdMin: Number(this.fieldConstraints[3].minProduction), // 陵水25-1最低产量

        // 其他参数
        ms: Number(this.form.productionTarget), // 计划产量
        unit: this.form.productionUnit, // 产量单位
      };

      // 根据模型类型决定是否包含利润目标字段
      if (this.modelType === "1") {
        apiData.ss = Number(this.form.profitTarget); // 利润目标（仅最大产量模型需要）
      }

      return apiData;
    },

    async startCalculation() {
      try {
        // 收集表单数据
        const formData = this.collectFormData();
        if (!formData) {
          return;
        }

        // 设置加载状态
        this.isLoading = true;
        const modelName = this.modelType === "1" ? "最大产量" : "最大销售额";
        this.$message.success(`开始测算（${modelName}）...`);

        // 根据模型类型调用不同的API
        let response;
        if (this.modelType === "1") {
          response = await maxOutput(formData);
        } else if (this.modelType === "2") {
          response = await maxSales(formData);
        } else {
          throw new Error("未知的模型类型");
        }

        // 处理响应数据
        if (response && response.code === 200) {
          this.$message.success("测算完成");
          this.isCalculating = true;

          // 保存API响应数据
          this.apiResponseData = response;
          console.log("API响应:", response);

          // 验证数据格式
          if (
            response.data &&
            response.data.result &&
            Array.isArray(response.data.result)
          ) {
            console.log("表格数据已更新");
          } else {
            console.warn("API响应数据格式不符合预期，使用示例数据");
            this.$message.warning("数据格式异常，显示示例数据");
          }
        } else {
          this.$message.error(response?.message || "测算失败");
        }
      } catch (error) {
        console.error("API调用失败:", error);

        // 处理不同类型的错误
        if (error.missingFields) {
          this.$message.error(
            `缺少必需参数: ${error.missingFields.join(", ")}`
          );
        } else if (error.invalidFields) {
          this.$message.error(
            `以下字段必须为数值类型: ${error.invalidFields.join(", ")}`
          );
        } else if (error.response) {
          // HTTP错误
          const status = error.response.status;
          const message =
            error.response.data?.message || `请求失败 (${status})`;
          this.$message.error(message);
        } else if (error.message) {
          // 其他已知错误
          this.$message.error(error.message);
        } else {
          // 未知错误
          this.$message.error("测算失败，请检查网络连接或联系技术支持");
        }
      } finally {
        // 无论成功还是失败都要清除加载状态
        this.isLoading = false;
      }
    },

    resetCalculation() {
      this.isCalculating = false;
      this.isLoading = false;

      // 清空API响应数据
      this.apiResponseData = null;

      // 清空表单数据
      this.form = {
        productionUnit: "",
        productionTarget: "",
        profitTarget: "",
      };

      // 清空销售渠道数据
      this.salesChannels.forEach((channel) => {
        channel.price = "";
        channel.expansionCoefficient = "";
      });

      // 清空供应策略数据
      this.supplyStrategy.forEach((strategy) => {
        strategy.minSupply = "";
        strategy.maxReceive = "";
      });

      // 清空气田约束数据
      this.fieldConstraints.forEach((field) => {
        field.minProduction = "";
        field.maxProduction = "";
      });

      this.$message.success("表单已重置");
    },

    /**
     * 表单验证辅助方法
     */
    validateForm() {
      const errors = [];

      // 验证基础参数
      if (!this.form.productionUnit?.trim()) errors.push("产量单位");
      if (!this.form.productionTarget) errors.push("计划产量目标");

      // 只有选择最大产量模型时才验证利润目标
      if (this.modelType === "1" && !this.form.profitTarget) {
        errors.push("利润目标");
      }

      // 验证销售渠道
      this.salesChannels.forEach((channel) => {
        if (!channel.price) errors.push(`${channel.channel}价格`);
        if (!channel.expansionCoefficient)
          errors.push(`${channel.channel}膨胀系数`);
      });

      // 验证供应策略
      this.supplyStrategy.forEach((strategy) => {
        if (!strategy.minSupply) errors.push(`${strategy.channel}最小供应量`);
        if (!strategy.maxReceive) errors.push(`${strategy.channel}最大接收量`);
      });

      // 验证气田约束
      this.fieldConstraints.forEach((field) => {
        if (!field.minProduction) errors.push(`${field.field}最低产量`);
        if (!field.maxProduction) errors.push(`${field.field}最高产量`);
      });

      return errors;
    },
  },
};
</script>

<style scoped>
.production-sales-model {
  padding: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-card {
  margin-bottom: 20px;
}

.section-box {
  display: flex;
}

.card-left {
  flex: 1;
  margin-right: 12px;
}

.card-right {
  flex: 1;
}

.action-buttons {
  text-align: center;
  margin-top: 30px;
  margin-bottom: 20px;
}

.result-title {
  display: flex;
  align-items: center;
  width: 100%;
  background: #ecf7ff;
  border-radius: 4px;
  box-sizing: border-box;
  border: 1px solid #1677ff;
}

.result-title-text {
  width: 100px;
  height: 26px;
  font-family: Source Han Sans;
  font-size: 14px;
  color: #fff;
  background: #1677ff;
  text-align: center;
}

.result-title-formula {
  color: #1677ff;
  font-family: Source Han Sans;
  font-size: 14px;
  margin-left: 20%;
}

/* 表格样式优化 */
.el-table {
  font-size: 14px;
}

.el-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: bold;
}

.el-table td {
  padding: 8px 0;
}

.el-table .el-table__fixed-right {
  background-color: #f9f9f9;
}

.el-table .el-table__fixed-left {
  background-color: #f9f9f9;
}

/* 总计行样式 */
.el-table__footer-wrapper .el-table__footer tr td {
  background-color: #e6f7ff;
  font-weight: bold;
  color: #1677ff;
}

/* 浅色主题适配 */
[data-theme="tint"] {
  .production-sales-model {
    background-color: #ffffff;
    color: #2e3641;
  }

  .box-card {
    background-color: #ffffff;
    border: 1px solid #eaeff5;
  }

  .section-card {
    background-color: #ffffff;
    border: 1px solid #eaeff5;
  }

  .result-title {
    background: #ecf7ff;
    border: 1px solid #1677ff;
  }

  .result-title-text {
    background: #1677ff;
    color: #fff;
  }

  .result-title-formula {
    color: #1677ff;
  }

  /* 表格样式 */
  .el-table {
    background-color: #ffffff;
  }

  .el-table th {
    background-color: #f5f7fa;
    color: #606266;
  }

  .el-table td {
    background-color: #ffffff;
    color: #2e3641;
  }

  .el-table .el-table__fixed-right,
  .el-table .el-table__fixed-left {
    background-color: #f9f9f9;
  }

  .el-table__footer-wrapper .el-table__footer tr td {
    background-color: #e6f7ff;
    color: #1677ff;
  }

  /* 标题样式 */
  h1,
  h2,
  h3 {
    color: #2e3641;
  }
}

/* 深色主题适配 */
[data-theme="dark"] .production-sales-model {
  background-color: #0c1324;
  color: #ffffff;

  .box-card {
    background-color: #1a2e52;
    border: 1px solid #4f98f6;
  }

  .section-card {
    background-color: #1a2e52;
    border: 1px solid #4f98f6;
  }

  .result-title {
    background: rgba(79, 152, 246, 0.1);
    border: 1px solid #4f98f6;
  }

  .result-title-text {
    background: #4f98f6;
    color: #ffffff;
  }

  .result-title-formula {
    color: #4f98f6;
  }

  /* 表格样式 */
  .el-table {
    background-color: #1a2e52;
  }

  .el-table th {
    background-color: #162549;
    color: #b3d3e5;
  }

  .el-table td {
    background-color: #1a2e52;
    color: #ffffff;
  }

  .el-table .el-table__fixed-right,
  .el-table .el-table__fixed-left {
    background-color: #162549;
  }

  .el-table__footer-wrapper .el-table__footer tr td {
    background-color: rgba(79, 152, 246, 0.2);
    color: #4f98f6;
  }

  /* 标题样式 */
  h1,
  h2,
  h3 {
    color: #ffffff;
  }

  /* 卡片头部样式 */
  ::v-deep .el-card__header {
    background-color: #162549;
    border-bottom: 1px solid #4f98f6;
    color: #ffffff;
  }

  /* 卡片内容样式 */
  ::v-deep .el-card__body {
    background-color: #1a2e52;
    color: #ffffff;
  }

  /* 表单标签样式 */
  ::v-deep .el-form-item__label {
    color: #b3d3e5;
  }

  /* 输入框样式 */
  ::v-deep .el-input__inner {
    background-color: #1a2e52;
    border-color: #4f98f6;
    color: #ffffff;
  }

  ::v-deep .el-input__inner:focus {
    border-color: #6ba4f4;
  }

  /* 下拉选择框样式 */
  ::v-deep .el-select .el-input__inner {
    background-color: #1a2e52;
    border-color: #4f98f6;
    color: #ffffff;
  }

  /* 按钮样式 */
  ::v-deep .el-button--primary {
    background-color: #4f98f6;
    border-color: #4f98f6;
  }

  ::v-deep .el-button--primary:hover {
    background-color: #6ba4f4;
    border-color: #6ba4f4;
  }

  /* 表格边框样式 */
  ::v-deep .el-table--border,
  ::v-deep .el-table--group {
    border-color: #4f98f6;
  }

  ::v-deep .el-table td.el-table__cell,
  ::v-deep .el-table th.el-table__cell.is-leaf {
    border-color: #4f98f6;
  }

  /* 表格悬停样式 */
  ::v-deep
    .el-table--enable-row-hover
    .el-table__body
    tr:hover
    > td.el-table__cell {
    background-color: rgba(79, 152, 246, 0.1);
  }

  /* 测算结果卡片深色主题适配 */
  .section-card {
    background-color: #1A2E52;
    border-color: #4F98F6;

    ::v-deep .el-card__header {
      background-color: #162549;
      border-bottom-color: #4F98F6;
      color: #ffffff;
    }

    ::v-deep .el-card__body {
      background-color: #1A2E52;
      color: #ffffff;
    }
  }

  /* 测算结果区域背景 */
  .section-box {
    background-color: #1A2E52;
  }

  .chart-box {
    background-color: #1A2E52;
  }

  /* 测算结果表格深色主题适配 */
  .card-left {
    ::v-deep .el-table {
      background-color: #1A2E52;
      color: #ffffff;
    }

    ::v-deep .el-table th.el-table__cell {
      background-color: #162549;
      color: #B3D3E5;
      border-color: #4F98F6;
    }

    ::v-deep .el-table td.el-table__cell {
      background-color: #1A2E52;
      color: #ffffff;
      border-color: #4F98F6;
    }

    ::v-deep .el-table--border,
    ::v-deep .el-table--group {
      border-color: #4F98F6;
    }

    ::v-deep .el-table--border::after,
    ::v-deep .el-table--group::after,
    ::v-deep .el-table::before {
      background-color: #4F98F6;
    }

    ::v-deep .el-table__fixed-right,
    ::v-deep .el-table__fixed-left {
      background-color: #162549;
    }

    ::v-deep .el-table__footer-wrapper .el-table__footer tr td {
      background-color: rgba(79, 152, 246, 0.2);
      color: #4F98F6;
      border-color: #4F98F6;
      font-weight: bold;
    }
  }

  /* 结果标题区域深色主题适配 */
  .result-title {
    background: rgba(79, 152, 246, 0.1);
    border-color: #4F98F6;
  }

  .result-title-text {
    background: #4F98F6;
    color: #ffffff;
  }

  .result-title-formula {
    color: #4F98F6;
  }
}

/* 浅色主题测算结果适配 */
[data-theme="tint"] {
  /* 测算结果卡片浅色主题适配 */
  .section-card {
    background-color: #ffffff;
    border-color: #EAEFF5;

    ::v-deep .el-card__header {
      background-color: #ffffff;
      border-bottom-color: #EAEFF5;
      color: #2E3641;
    }

    ::v-deep .el-card__body {
      background-color: #ffffff;
      color: #2E3641;
    }
  }

  /* 测算结果区域背景 */
  .section-box {
    background-color: #ffffff;
  }

  .chart-box {
    background-color: #ffffff;
  }

  /* 测算结果表格浅色主题适配 */
  .card-left {
    ::v-deep .el-table {
      background-color: #ffffff;
      color: #2E3641;
    }

    ::v-deep .el-table th.el-table__cell {
      background-color: #f5f7fa;
      color: #606266;
      border-color: #EAEFF5;
    }

    ::v-deep .el-table td.el-table__cell {
      background-color: #ffffff;
      color: #2E3641;
      border-color: #EAEFF5;
    }

    ::v-deep .el-table--border,
    ::v-deep .el-table--group {
      border-color: #EAEFF5;
    }

    ::v-deep .el-table--border::after,
    ::v-deep .el-table--group::after,
    ::v-deep .el-table::before {
      background-color: #EAEFF5;
    }

    ::v-deep .el-table__fixed-right,
    ::v-deep .el-table__fixed-left {
      background-color: #f9f9f9;
    }

    ::v-deep .el-table__footer-wrapper .el-table__footer tr td {
      background-color: #e6f7ff;
      color: #1677ff;
      border-color: #EAEFF5;
      font-weight: bold;
    }
  }

  /* 结果标题区域浅色主题适配 */
  .result-title {
    background: #ecf7ff;
    border-color: #1677ff;
  }

  .result-title-text {
    background: #1677ff;
    color: #ffffff;
  }

  .result-title-formula {
    color: #1677ff;
  }
}

</style>
